import { loadSampleData } from '../utils/sampleData';

/**
 * <PERSON><PERSON>t to load sample data for testing
 * This can be called from the app during development
 */
export const initializeSampleData = async (): Promise<void> => {
  try {
    console.log('Loading sample data...');
    await loadSampleData();
    console.log('Sample data loaded successfully!');
  } catch (error) {
    console.error('Failed to load sample data:', error);
  }
};

// Uncomment the line below to automatically load sample data when this file is imported
// initializeSampleData();
