/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string = string> extends Record<string, unknown> {
      StaticRoutes: `/` | `/(tabs)` | `/(tabs)/` | `/(tabs)/balances` | `/(tabs)/expenses` | `/(tabs)/profile` | `/_sitemap` | `/balances` | `/expense/add` | `/expenses` | `/profile` | `/trip/add`;
      DynamicRoutes: `/trip/${Router.SingleRoutePart<T>}`;
      DynamicRouteTemplate: `/trip/[id]`;
    }
  }
}
