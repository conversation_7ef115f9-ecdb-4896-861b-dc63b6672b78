# TripSplit 🧳💰

**TripSplit** is a React Native mobile app designed to help groups of friends manage shared expenses during trips and split them fairly. Built with Expo Router, Nativewind (Tailwind CSS), and TypeScript.

## 🎯 Features

### ✅ Core Features
- **Trip Management**: Create, edit, and delete trips with dates and member lists
- **Expense Tracking**: Add expenses with flexible splitting options
- **Smart Splitting**: Equal split, custom amounts, or percentage-based splits
- **Balance Calculations**: Real-time balance tracking for each member
- **Settlement Suggestions**: Minimal transaction recommendations to settle all debts
- **Local Storage**: All data stored locally using AsyncStorage (no backend required)

### 🎨 UI/UX Features
- **Modern Design**: Clean interface using Tailwind CSS via Nativewind
- **Dark Mode Support**: Automatic theme switching based on system preferences
- **Arabic Support**: RTL layout and Arabic text support
- **Responsive Design**: Optimized for mobile devices
- **Intuitive Navigation**: Tab-based navigation with stack screens

### 📊 Data Features
- **Real-time Calculations**: Automatic balance and settlement calculations
- **Multiple Currencies**: Support for EGP, USD, EUR, SAR, AED
- **Expense Categories**: Organize expenses by type
- **Detailed Reports**: View expenses by trip or across all trips

## 🛠 Tech Stack

- **Framework**: React Native with Expo
- **Navigation**: Expo Router (file-based routing)
- **Styling**: Nativewind (Tailwind CSS for React Native)
- **State Management**: Zustand
- **Storage**: AsyncStorage for local data persistence
- **Language**: TypeScript
- **Icons**: Expo Vector Icons (Ionicons)

## 📱 Screens

### Tab Navigation
1. **الرحلات (Trips)**: List of all trips with summary information
2. **المصروفات (Expenses)**: All expenses across trips with filtering
3. **الأرصدة (Balances)**: Balance overview and settlement suggestions
4. **الإعدادات (Settings)**: App settings and statistics

### Modal Screens
- **Trip Details**: Detailed view with expenses, balances, and settlements
- **Add Trip**: Create new trip with members
- **Add Expense**: Add expense with flexible splitting options
- **Edit Trip/Expense**: Modify existing data

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (or physical device with Expo Go)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd EL-REHLAH
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

4. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app on physical device

## 📁 Project Structure

```
app/
├── (tabs)/                 # Tab navigation screens
│   ├── index.tsx          # Trips list
│   ├── expenses.tsx       # Expenses overview
│   ├── balances.tsx       # Balances and settlements
│   └── profile.tsx        # Settings and profile
├── trip/
│   ├── [id].tsx          # Trip details
│   └── add.tsx           # Add new trip
├── expense/
│   └── add.tsx           # Add new expense
└── _layout.tsx           # Root layout

components/               # Reusable UI components
store/                   # Zustand state management
types/                   # TypeScript type definitions
utils/                   # Utility functions
├── storage.ts          # AsyncStorage helpers
├── calculations.ts     # Balance and settlement calculations
└── sampleData.ts       # Sample data for testing
```

## 💾 Data Structure

### Trip
```typescript
interface Trip {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  members: User[];
  expenses: Expense[];
  createdAt: string;
  updatedAt: string;
}
```

### Expense
```typescript
interface Expense {
  id: string;
  tripId: string;
  description: string;
  amount: number;
  currency: string;
  date: string;
  paidBy: string[];
  splitBetween: SplitInfo[];
  category?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
```

## 🧮 Smart Splitting Algorithm

The app implements an intelligent debt settlement algorithm that:

1. **Calculates individual balances** for each trip member
2. **Identifies creditors** (people owed money) and **debtors** (people who owe money)
3. **Suggests minimal transactions** to settle all debts efficiently
4. **Supports multiple splitting methods**:
   - Equal split among all participants
   - Custom amounts for each person
   - Percentage-based splits

## 🌟 Key Features Explained

### Balance Calculation
- Tracks how much each person paid vs. how much they owe
- Calculates net balance (positive = owed money, negative = owes money)
- Updates in real-time as expenses are added/modified

### Settlement Optimization
- Uses a greedy algorithm to minimize the number of transactions
- Instead of everyone paying everyone, suggests optimal transfers
- Example: If A owes B $50 and B owes C $50, suggests A pays C directly

### Flexible Expense Splitting
- **Equal Split**: Divides amount equally among selected members
- **Custom Split**: Allows specific amounts for each person
- **Partial Participation**: Not everyone needs to participate in every expense

## 🎨 Styling with Nativewind

The app uses Nativewind for styling, which brings Tailwind CSS to React Native:

```tsx
// Example component styling
<View className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
  <Text className="text-lg font-bold text-gray-900 dark:text-white">
    Trip Name
  </Text>
</View>
```

### Theme Support
- Automatic dark/light mode based on system preferences
- Consistent color scheme across all screens
- Responsive design for different screen sizes

## 📱 Sample Data

The app includes sample data for testing:

```typescript
// Sample trip with expenses
{
  name: 'رحلة الغردقة',
  members: ['محمد', 'علي', 'خالد'],
  expenses: [
    { description: 'فندق', amount: 1500, paidBy: ['محمد'] },
    { description: 'مطعم', amount: 450, paidBy: ['علي'] },
    // ...
  ]
}
```

## 🔮 Future Enhancements

### Planned Features
- [ ] **Cloud Sync**: Firebase integration for data backup and sharing
- [ ] **PDF Export**: Generate trip reports and expense summaries
- [ ] **Charts & Analytics**: Visual expense breakdown with charts
- [ ] **Receipt Photos**: Attach photos to expenses
- [ ] **Multi-language**: Full English/Arabic localization
- [ ] **Currency Conversion**: Real-time exchange rates
- [ ] **Expense Categories**: Predefined categories with icons
- [ ] **Trip Templates**: Save and reuse trip configurations
- [ ] **Notifications**: Reminders for pending settlements
- [ ] **Group Chat**: In-app messaging for trip coordination

### Technical Improvements
- [ ] **Offline Support**: Better offline functionality
- [ ] **Performance**: Optimize for large datasets
- [ ] **Testing**: Unit and integration tests
- [ ] **CI/CD**: Automated build and deployment
- [ ] **Analytics**: Usage tracking and crash reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Abo Ellil**
- Email: <EMAIL>

## 🙏 Acknowledgments

- Built with [Expo](https://expo.dev/)
- Styled with [Nativewind](https://www.nativewind.dev/)
- State management with [Zustand](https://github.com/pmndrs/zustand)
- Icons by [Expo Vector Icons](https://icons.expo.fyi/)

---

**Happy Trip Splitting! 🎉**
