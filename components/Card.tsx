import React from 'react';
import { View, ViewProps } from 'react-native';

interface CardProps extends ViewProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
}

export default function Card({
  children,
  variant = 'default',
  padding = 'md',
  className = '',
  ...props
}: CardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'elevated':
        return 'bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700';
      case 'outlined':
        return 'bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600';
      default:
        return 'bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700';
    }
  };

  const getPaddingStyles = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'sm':
        return 'p-2';
      case 'md':
        return 'p-4';
      case 'lg':
        return 'p-6';
      default:
        return 'p-4';
    }
  };

  return (
    <View
      className={`
        rounded-lg
        ${getVariantStyles()}
        ${getPaddingStyles()}
        ${className}
      `}
      {...props}
    >
      {children}
    </View>
  );
}
