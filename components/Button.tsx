import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  className?: string;
}

export default function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  icon,
  className = '',
}: ButtonProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-primary-600 active:bg-primary-700';
      case 'secondary':
        return 'bg-secondary-600 active:bg-secondary-700';
      case 'danger':
        return 'bg-danger-600 active:bg-danger-700';
      case 'outline':
        return 'bg-transparent border-2 border-primary-600 active:bg-primary-50 dark:active:bg-primary-900/20';
      default:
        return 'bg-primary-600 active:bg-primary-700';
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2';
      case 'md':
        return 'px-4 py-3';
      case 'lg':
        return 'px-6 py-4';
      default:
        return 'px-4 py-3';
    }
  };

  const getTextStyles = () => {
    const baseStyles = 'font-semibold text-center';
    const sizeStyles = size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base';
    const colorStyles = variant === 'outline' 
      ? 'text-primary-600 dark:text-primary-400' 
      : 'text-white';
    
    return `${baseStyles} ${sizeStyles} ${colorStyles}`;
  };

  const isDisabled = disabled || loading;

  return (
    <TouchableOpacity
      className={`
        rounded-lg flex-row items-center justify-center
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${isDisabled ? 'opacity-50' : ''}
        ${className}
      `}
      onPress={onPress}
      disabled={isDisabled}
    >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={variant === 'outline' ? '#3b82f6' : 'white'} 
        />
      ) : (
        <>
          {icon && (
            <Ionicons 
              name={icon} 
              size={size === 'sm' ? 16 : size === 'lg' ? 24 : 20} 
              color={variant === 'outline' ? '#3b82f6' : 'white'}
              style={{ marginRight: 8 }}
            />
          )}
          <Text className={getTextStyles()}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
}
