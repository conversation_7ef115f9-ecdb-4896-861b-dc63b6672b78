import React from 'react';
import { View, Text, TextInput, TextInputProps } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  required?: boolean;
  containerClassName?: string;
}

export default function Input({
  label,
  error,
  icon,
  required = false,
  containerClassName = '',
  className = '',
  ...props
}: InputProps) {
  return (
    <View className={`mb-4 ${containerClassName}`}>
      {label && (
        <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {label}
          {required && <Text className="text-danger-600"> *</Text>}
        </Text>
      )}
      
      <View className="relative">
        {icon && (
          <View className="absolute left-3 top-3 z-10">
            <Ionicons name={icon} size={20} color="#9ca3af" />
          </View>
        )}
        
        <TextInput
          className={`
            bg-white dark:bg-gray-800 
            border border-gray-300 dark:border-gray-600 
            rounded-lg px-4 py-3 
            text-gray-900 dark:text-white
            ${icon ? 'pl-12' : ''}
            ${error ? 'border-danger-500' : ''}
            ${className}
          `}
          placeholderTextColor="#9ca3af"
          {...props}
        />
      </View>
      
      {error && (
        <Text className="text-danger-600 text-sm mt-1">
          {error}
        </Text>
      )}
    </View>
  );
}
