export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
}

export interface Trip {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  members: User[];
  expenses: Expense[];
  createdAt: string;
  updatedAt: string;
}

export interface Expense {
  id: string;
  tripId: string;
  description: string;
  amount: number;
  currency: string;
  date: string;
  paidBy: string[]; // User IDs
  splitBetween: SplitInfo[];
  category?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SplitInfo {
  userId: string;
  amount?: number; // For custom splits
  percentage?: number; // For percentage splits
}

export interface Balance {
  userId: string;
  userName: string;
  totalPaid: number;
  totalOwed: number;
  netBalance: number; // positive = owed money, negative = owes money
}

export interface Settlement {
  from: string; // User ID
  to: string; // User ID
  amount: number;
  fromName: string;
  toName: string;
}

export interface ExpenseCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export type SplitType = 'equal' | 'custom' | 'percentage';

export interface AppSettings {
  defaultCurrency: string;
  language: 'en' | 'ar';
  theme: 'light' | 'dark' | 'auto';
}

export interface ExpenseFormData {
  description: string;
  amount: string;
  date: string;
  paidBy: string[];
  splitBetween: string[];
  splitType: SplitType;
  customSplits?: { [userId: string]: number };
  category?: string;
  notes?: string;
}
