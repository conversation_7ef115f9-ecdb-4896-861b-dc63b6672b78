import { create } from 'zustand';
import { Trip, Expense, User, AppSettings, Balance, Settlement } from '../types';
import { 
  saveTrips, 
  loadTrips, 
  saveSettings, 
  loadSettings 
} from '../utils/storage';
import { 
  calculateBalances, 
  calculateSettlements 
} from '../utils/calculations';

interface AppStore {
  // State
  trips: Trip[];
  currentTrip: Trip | null;
  settings: AppSettings;
  loading: boolean;
  
  // Actions
  initializeApp: () => Promise<void>;
  
  // Trip actions
  addTrip: (trip: Omit<Trip, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTrip: (id: string, updates: Partial<Trip>) => Promise<void>;
  deleteTrip: (id: string) => Promise<void>;
  setCurrentTrip: (trip: Trip | null) => void;
  
  // Expense actions
  addExpense: (tripId: string, expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateExpense: (tripId: string, expenseId: string, updates: Partial<Expense>) => Promise<void>;
  deleteExpense: (tripId: string, expenseId: string) => Promise<void>;
  
  // User actions
  addMemberToTrip: (tripId: string, user: User) => Promise<void>;
  removeMemberFromTrip: (tripId: string, userId: string) => Promise<void>;
  
  // Calculations
  getTripBalances: (tripId: string) => Balance[];
  getTripSettlements: (tripId: string) => Settlement[];
  
  // Settings
  updateSettings: (settings: Partial<AppSettings>) => Promise<void>;
}

export const useStore = create<AppStore>((set, get) => ({
  trips: [],
  currentTrip: null,
  settings: {
    defaultCurrency: 'EGP',
    language: 'ar',
    theme: 'auto',
  },
  loading: false,

  initializeApp: async () => {
    set({ loading: true });
    try {
      const [trips, settings] = await Promise.all([
        loadTrips(),
        loadSettings(),
      ]);
      set({ 
        trips, 
        settings: { ...get().settings, ...settings },
        loading: false 
      });
    } catch (error) {
      console.error('Failed to initialize app:', error);
      set({ loading: false });
    }
  },

  addTrip: async (tripData) => {
    const newTrip: Trip = {
      ...tripData,
      id: `trip_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      expenses: [],
    };
    
    const trips = [...get().trips, newTrip];
    set({ trips });
    await saveTrips(trips);
  },

  updateTrip: async (id, updates) => {
    const trips = get().trips.map(trip => 
      trip.id === id 
        ? { ...trip, ...updates, updatedAt: new Date().toISOString() }
        : trip
    );
    set({ trips });
    await saveTrips(trips);
    
    // Update current trip if it's the one being updated
    const currentTrip = get().currentTrip;
    if (currentTrip?.id === id) {
      set({ currentTrip: trips.find(t => t.id === id) || null });
    }
  },

  deleteTrip: async (id) => {
    const trips = get().trips.filter(trip => trip.id !== id);
    set({ trips });
    await saveTrips(trips);
    
    // Clear current trip if it's the one being deleted
    if (get().currentTrip?.id === id) {
      set({ currentTrip: null });
    }
  },

  setCurrentTrip: (trip) => {
    set({ currentTrip: trip });
  },

  addExpense: async (tripId, expenseData) => {
    const newExpense: Expense = {
      ...expenseData,
      id: `exp_${Date.now()}`,
      tripId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    const trips = get().trips.map(trip => 
      trip.id === tripId 
        ? { 
            ...trip, 
            expenses: [...trip.expenses, newExpense],
            updatedAt: new Date().toISOString()
          }
        : trip
    );
    
    set({ trips });
    await saveTrips(trips);
    
    // Update current trip if it's the one being updated
    const currentTrip = get().currentTrip;
    if (currentTrip?.id === tripId) {
      set({ currentTrip: trips.find(t => t.id === tripId) || null });
    }
  },

  updateExpense: async (tripId, expenseId, updates) => {
    const trips = get().trips.map(trip => 
      trip.id === tripId 
        ? {
            ...trip,
            expenses: trip.expenses.map(expense =>
              expense.id === expenseId
                ? { ...expense, ...updates, updatedAt: new Date().toISOString() }
                : expense
            ),
            updatedAt: new Date().toISOString()
          }
        : trip
    );
    
    set({ trips });
    await saveTrips(trips);
    
    // Update current trip if it's the one being updated
    const currentTrip = get().currentTrip;
    if (currentTrip?.id === tripId) {
      set({ currentTrip: trips.find(t => t.id === tripId) || null });
    }
  },

  deleteExpense: async (tripId, expenseId) => {
    const trips = get().trips.map(trip => 
      trip.id === tripId 
        ? {
            ...trip,
            expenses: trip.expenses.filter(expense => expense.id !== expenseId),
            updatedAt: new Date().toISOString()
          }
        : trip
    );
    
    set({ trips });
    await saveTrips(trips);
    
    // Update current trip if it's the one being updated
    const currentTrip = get().currentTrip;
    if (currentTrip?.id === tripId) {
      set({ currentTrip: trips.find(t => t.id === tripId) || null });
    }
  },

  addMemberToTrip: async (tripId, user) => {
    const trips = get().trips.map(trip => 
      trip.id === tripId 
        ? { 
            ...trip, 
            members: [...trip.members, user],
            updatedAt: new Date().toISOString()
          }
        : trip
    );
    
    set({ trips });
    await saveTrips(trips);
    
    // Update current trip if it's the one being updated
    const currentTrip = get().currentTrip;
    if (currentTrip?.id === tripId) {
      set({ currentTrip: trips.find(t => t.id === tripId) || null });
    }
  },

  removeMemberFromTrip: async (tripId, userId) => {
    const trips = get().trips.map(trip => 
      trip.id === tripId 
        ? { 
            ...trip, 
            members: trip.members.filter(member => member.id !== userId),
            updatedAt: new Date().toISOString()
          }
        : trip
    );
    
    set({ trips });
    await saveTrips(trips);
    
    // Update current trip if it's the one being updated
    const currentTrip = get().currentTrip;
    if (currentTrip?.id === tripId) {
      set({ currentTrip: trips.find(t => t.id === tripId) || null });
    }
  },

  getTripBalances: (tripId) => {
    const trip = get().trips.find(t => t.id === tripId);
    if (!trip) return [];
    return calculateBalances(trip);
  },

  getTripSettlements: (tripId) => {
    const trip = get().trips.find(t => t.id === tripId);
    if (!trip) return [];
    const balances = calculateBalances(trip);
    return calculateSettlements(balances);
  },

  updateSettings: async (newSettings) => {
    const settings = { ...get().settings, ...newSettings };
    set({ settings });
    await saveSettings(settings);
  },
}));
