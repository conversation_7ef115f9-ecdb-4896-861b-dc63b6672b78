import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { formatDate, formatCurrency } from '../../utils/calculations';

export default function TripDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { 
    trips, 
    loading, 
    initializeApp, 
    deleteExpense, 
    getTripBalances, 
    getTripSettlements,
    setCurrentTrip 
  } = useStore();
  
  const [activeTab, setActiveTab] = useState<'expenses' | 'balances' | 'settlements'>('expenses');
  
  const trip = trips.find(t => t.id === id);
  
  if (!trip) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50 dark:bg-gray-900">
        <Text className="text-lg text-gray-500 dark:text-gray-400">
          الرحلة غير موجودة
        </Text>
      </View>
    );
  }

  const balances = getTripBalances(trip.id);
  const settlements = getTripSettlements(trip.id);
  const totalExpenses = trip.expenses.reduce((sum, exp) => sum + exp.amount, 0);

  const handleDeleteExpense = (expenseId: string) => {
    const expense = trip.expenses.find(e => e.id === expenseId);
    if (!expense) return;

    Alert.alert(
      'حذف المصروف',
      `هل أنت متأكد من حذف "${expense.description}"؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: () => deleteExpense(trip.id, expenseId),
        },
      ]
    );
  };

  const getUserName = (userId: string): string => {
    const user = trip.members.find(member => member.id === userId);
    return user?.name || 'مستخدم غير معروف';
  };

  const renderExpenseCard = (expense: any) => (
    <View 
      key={expense.id}
      className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 shadow-sm border border-gray-200 dark:border-gray-700"
    >
      <View className="flex-row justify-between items-start mb-2">
        <Text className="text-lg font-semibold text-gray-900 dark:text-white flex-1">
          {expense.description}
        </Text>
        <View className="flex-row items-center">
          <Text className="text-lg font-bold text-primary-600 mr-2">
            {formatCurrency(expense.amount, expense.currency)}
          </Text>
          <TouchableOpacity
            onPress={() => handleDeleteExpense(expense.id)}
            className="p-1"
          >
            <Ionicons name="trash-outline" size={18} color="#ef4444" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="calendar-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          {formatDate(expense.date)}
        </Text>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="person-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          دفع: {expense.paidBy.map((id: string) => getUserName(id)).join(', ')}
        </Text>
      </View>
      
      <View className="flex-row items-center">
        <Ionicons name="people-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          مقسم على: {expense.splitBetween.length} أشخاص
        </Text>
      </View>
      
      {expense.notes && (
        <View className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          <Text className="text-gray-600 dark:text-gray-400 text-sm">
            {expense.notes}
          </Text>
        </View>
      )}
    </View>
  );

  const renderBalanceCard = (balance: any) => (
    <View 
      key={balance.userId}
      className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 shadow-sm border border-gray-200 dark:border-gray-700"
    >
      <View className="flex-row justify-between items-center mb-3">
        <Text className="text-lg font-semibold text-gray-900 dark:text-white">
          {balance.userName}
        </Text>
        <View className={`px-3 py-1 rounded-full ${
          balance.netBalance > 0 
            ? 'bg-secondary-100 dark:bg-secondary-900/30' 
            : balance.netBalance < 0
            ? 'bg-danger-100 dark:bg-danger-900/30'
            : 'bg-gray-100 dark:bg-gray-700'
        }`}>
          <Text className={`font-bold ${
            balance.netBalance > 0 
              ? 'text-secondary-700 dark:text-secondary-300' 
              : balance.netBalance < 0
              ? 'text-danger-700 dark:text-danger-300'
              : 'text-gray-700 dark:text-gray-300'
          }`}>
            {balance.netBalance > 0 ? '+' : ''}{formatCurrency(balance.netBalance)}
          </Text>
        </View>
      </View>
      
      <View className="space-y-2">
        <View className="flex-row justify-between">
          <Text className="text-gray-600 dark:text-gray-400">دفع:</Text>
          <Text className="text-gray-900 dark:text-white font-medium">
            {formatCurrency(balance.totalPaid)}
          </Text>
        </View>
        <View className="flex-row justify-between">
          <Text className="text-gray-600 dark:text-gray-400">يدين بـ:</Text>
          <Text className="text-gray-900 dark:text-white font-medium">
            {formatCurrency(balance.totalOwed)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderSettlementCard = (settlement: any, index: number) => (
    <View 
      key={`${settlement.from}-${settlement.to}-${index}`}
      className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 shadow-sm border border-gray-200 dark:border-gray-700"
    >
      <View className="flex-row items-center justify-between">
        <View className="flex-1">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white">
            {settlement.fromName}
          </Text>
          <View className="flex-row items-center mt-1">
            <Ionicons name="arrow-forward" size={16} color="#6b7280" />
            <Text className="text-gray-600 dark:text-gray-400 ml-2">
              {settlement.toName}
            </Text>
          </View>
        </View>
        <Text className="text-lg font-bold text-primary-600">
          {formatCurrency(settlement.amount)}
        </Text>
      </View>
    </View>
  );

  const TabButton = ({ 
    tab, 
    title, 
    count 
  }: { 
    tab: 'expenses' | 'balances' | 'settlements'; 
    title: string; 
    count?: number;
  }) => (
    <TouchableOpacity
      className={`flex-1 py-3 px-4 ${
        activeTab === tab 
          ? 'border-b-2 border-primary-600' 
          : 'border-b border-gray-200 dark:border-gray-700'
      }`}
      onPress={() => setActiveTab(tab)}
    >
      <Text className={`text-center font-medium ${
        activeTab === tab 
          ? 'text-primary-600' 
          : 'text-gray-600 dark:text-gray-400'
      }`}>
        {title}
        {count !== undefined && ` (${count})`}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={initializeApp}
            tintColor="#3b82f6"
          />
        }
      >
        {/* Trip Header */}
        <View className="bg-white dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
          <Text className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {trip.name}
          </Text>
          
          <View className="flex-row items-center mb-2">
            <Ionicons name="calendar-outline" size={16} color="#6b7280" />
            <Text className="text-gray-600 dark:text-gray-400 ml-2">
              {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
            </Text>
          </View>
          
          <View className="flex-row items-center mb-3">
            <Ionicons name="people-outline" size={16} color="#6b7280" />
            <Text className="text-gray-600 dark:text-gray-400 ml-2">
              {trip.members.map(m => m.name).join(', ')}
            </Text>
          </View>
          
          <View className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3">
            <Text className="text-center text-lg font-bold text-primary-700 dark:text-primary-300">
              إجمالي المصروفات: {formatCurrency(totalExpenses)}
            </Text>
          </View>
        </View>

        {/* Tabs */}
        <View className="bg-white dark:bg-gray-800 flex-row border-b border-gray-200 dark:border-gray-700">
          <TabButton tab="expenses" title="المصروفات" count={trip.expenses.length} />
          <TabButton tab="balances" title="الأرصدة" count={balances.length} />
          <TabButton tab="settlements" title="التسويات" count={settlements.length} />
        </View>

        {/* Content */}
        <View className="p-4">
          {activeTab === 'expenses' && (
            <View>
              {trip.expenses.length === 0 ? (
                <View className="py-16 items-center">
                  <Ionicons name="receipt-outline" size={60} color="#9ca3af" />
                  <Text className="text-lg font-medium text-gray-500 dark:text-gray-400 mt-4">
                    لا توجد مصروفات
                  </Text>
                  <Text className="text-gray-400 dark:text-gray-500 mt-2 text-center">
                    ابدأ بإضافة مصروفات لهذه الرحلة
                  </Text>
                </View>
              ) : (
                trip.expenses.map(renderExpenseCard)
              )}
            </View>
          )}

          {activeTab === 'balances' && (
            <View>
              {balances.length === 0 ? (
                <View className="py-16 items-center">
                  <Ionicons name="analytics-outline" size={60} color="#9ca3af" />
                  <Text className="text-lg font-medium text-gray-500 dark:text-gray-400 mt-4">
                    لا توجد أرصدة
                  </Text>
                </View>
              ) : (
                balances.map(renderBalanceCard)
              )}
            </View>
          )}

          {activeTab === 'settlements' && (
            <View>
              {settlements.length === 0 ? (
                <View className="py-16 items-center">
                  <Ionicons name="checkmark-circle-outline" size={60} color="#22c55e" />
                  <Text className="text-lg font-medium text-secondary-600 dark:text-secondary-400 mt-4">
                    جميع الحسابات متوازنة!
                  </Text>
                  <Text className="text-gray-400 dark:text-gray-500 mt-2 text-center">
                    لا توجد تسويات مطلوبة
                  </Text>
                </View>
              ) : (
                <>
                  <View className="bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-3 mb-4">
                    <Text className="text-secondary-700 dark:text-secondary-300 font-medium text-center">
                      {settlements.length} تحويل مطلوب لتسوية جميع الديون
                    </Text>
                  </View>
                  {settlements.map(renderSettlementCard)}
                </>
              )}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Add Expense FAB */}
      <TouchableOpacity
        className="absolute bottom-6 right-6 bg-primary-600 w-14 h-14 rounded-full justify-center items-center shadow-lg"
        onPress={() => {
          setCurrentTrip(trip);
          router.push('/expense/add');
        }}
      >
        <Ionicons name="add" size={28} color="white" />
      </TouchableOpacity>
    </View>
  );
}
