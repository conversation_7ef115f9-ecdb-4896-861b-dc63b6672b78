import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { User } from '../../types';
import { generateUserId } from '../../utils/calculations';

export default function AddTripScreen() {
  const { addTrip } = useStore();
  
  const [tripName, setTripName] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [members, setMembers] = useState<User[]>([]);
  const [newMemberName, setNewMemberName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddMember = () => {
    if (!newMemberName.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال اسم العضو');
      return;
    }

    const newMember: User = {
      id: generateUserId(),
      name: newMemberName.trim(),
    };

    setMembers([...members, newMember]);
    setNewMemberName('');
  };

  const handleRemoveMember = (memberId: string) => {
    setMembers(members.filter(member => member.id !== memberId));
  };

  const handleSaveTrip = async () => {
    // Validation
    if (!tripName.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال اسم الرحلة');
      return;
    }

    if (!startDate) {
      Alert.alert('خطأ', 'يرجى إدخال تاريخ البداية');
      return;
    }

    if (!endDate) {
      Alert.alert('خطأ', 'يرجى إدخال تاريخ النهاية');
      return;
    }

    if (new Date(startDate) > new Date(endDate)) {
      Alert.alert('خطأ', 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
      return;
    }

    if (members.length < 2) {
      Alert.alert('خطأ', 'يجب إضافة عضوين على الأقل');
      return;
    }

    setLoading(true);
    try {
      await addTrip({
        name: tripName.trim(),
        startDate,
        endDate,
        members,
      });
      
      Alert.alert('نجح', 'تم إنشاء الرحلة بنجاح', [
        { text: 'موافق', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('خطأ', 'فشل في إنشاء الرحلة');
    } finally {
      setLoading(false);
    }
  };

  const formatDateForInput = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  const handleDateChange = (value: string, setter: (date: string) => void) => {
    // Simple date validation and formatting
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (dateRegex.test(value) || value === '') {
      setter(value);
    }
  };

  return (
    <KeyboardAvoidingView 
      className="flex-1 bg-gray-50 dark:bg-gray-900"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView className="flex-1 p-4">
        {/* Trip Name */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            اسم الرحلة *
          </Text>
          <TextInput
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
            placeholder="مثال: رحلة الغردقة"
            placeholderTextColor="#9ca3af"
            value={tripName}
            onChangeText={setTripName}
          />
        </View>

        {/* Dates */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            التواريخ *
          </Text>
          
          <View className="mb-3">
            <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              تاريخ البداية
            </Text>
            <TextInput
              className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
              placeholder="YYYY-MM-DD"
              placeholderTextColor="#9ca3af"
              value={startDate}
              onChangeText={(value) => handleDateChange(value, setStartDate)}
              keyboardType="numeric"
            />
          </View>
          
          <View>
            <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              تاريخ النهاية
            </Text>
            <TextInput
              className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
              placeholder="YYYY-MM-DD"
              placeholderTextColor="#9ca3af"
              value={endDate}
              onChangeText={(value) => handleDateChange(value, setEndDate)}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Members */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            الأعضاء * (على الأقل 2)
          </Text>
          
          {/* Add Member Input */}
          <View className="flex-row mb-3">
            <TextInput
              className="flex-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white mr-2"
              placeholder="اسم العضو"
              placeholderTextColor="#9ca3af"
              value={newMemberName}
              onChangeText={setNewMemberName}
              onSubmitEditing={handleAddMember}
            />
            <TouchableOpacity
              className="bg-primary-600 px-4 py-3 rounded-lg justify-center items-center"
              onPress={handleAddMember}
            >
              <Ionicons name="add" size={20} color="white" />
            </TouchableOpacity>
          </View>
          
          {/* Members List */}
          {members.map((member) => (
            <View
              key={member.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 mb-2 flex-row justify-between items-center"
            >
              <Text className="text-gray-900 dark:text-white font-medium">
                {member.name}
              </Text>
              <TouchableOpacity
                onPress={() => handleRemoveMember(member.id)}
                className="p-1"
              >
                <Ionicons name="close-circle" size={20} color="#ef4444" />
              </TouchableOpacity>
            </View>
          ))}
          
          {members.length === 0 && (
            <View className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 items-center">
              <Ionicons name="people-outline" size={40} color="#9ca3af" />
              <Text className="text-gray-500 dark:text-gray-400 mt-2">
                لم يتم إضافة أعضاء بعد
              </Text>
            </View>
          )}
        </View>

        {/* Save Button */}
        <TouchableOpacity
          className={`py-4 rounded-lg items-center ${
            loading 
              ? 'bg-gray-400' 
              : 'bg-primary-600'
          }`}
          onPress={handleSaveTrip}
          disabled={loading}
        >
          <Text className="text-white font-semibold text-lg">
            {loading ? 'جاري الحفظ...' : 'إنشاء الرحلة'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
