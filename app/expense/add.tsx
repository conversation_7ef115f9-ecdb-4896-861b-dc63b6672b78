import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { SplitType, SplitInfo } from '../../types';

export default function AddExpenseScreen() {
  const { currentTrip, addExpense, settings } = useStore();
  
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [paidBy, setPaidBy] = useState<string[]>([]);
  const [splitType, setSplitType] = useState<SplitType>('equal');
  const [splitBetween, setSplitBetween] = useState<string[]>([]);
  const [customSplits, setCustomSplits] = useState<{ [userId: string]: string }>({});
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  if (!currentTrip) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50 dark:bg-gray-900">
        <Text className="text-lg text-gray-500 dark:text-gray-400">
          يرجى اختيار رحلة أولاً
        </Text>
      </View>
    );
  }

  const handleTogglePaidBy = (userId: string) => {
    if (paidBy.includes(userId)) {
      setPaidBy(paidBy.filter(id => id !== userId));
    } else {
      setPaidBy([...paidBy, userId]);
    }
  };

  const handleToggleSplitBetween = (userId: string) => {
    if (splitBetween.includes(userId)) {
      setSplitBetween(splitBetween.filter(id => id !== userId));
      // Remove custom split if exists
      const newCustomSplits = { ...customSplits };
      delete newCustomSplits[userId];
      setCustomSplits(newCustomSplits);
    } else {
      setSplitBetween([...splitBetween, userId]);
    }
  };

  const handleSelectAllMembers = () => {
    const allMemberIds = currentTrip.members.map(m => m.id);
    setSplitBetween(allMemberIds);
  };

  const handleCustomSplitChange = (userId: string, value: string) => {
    setCustomSplits({
      ...customSplits,
      [userId]: value,
    });
  };

  const validateCustomSplits = (): boolean => {
    if (splitType !== 'custom') return true;
    
    const totalAmount = parseFloat(amount);
    if (isNaN(totalAmount)) return false;
    
    let customTotal = 0;
    for (const userId of splitBetween) {
      const customAmount = parseFloat(customSplits[userId] || '0');
      if (isNaN(customAmount) || customAmount < 0) return false;
      customTotal += customAmount;
    }
    
    return Math.abs(customTotal - totalAmount) < 0.01;
  };

  const handleSaveExpense = async () => {
    // Validation
    if (!description.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال وصف المصروف');
      return;
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      Alert.alert('خطأ', 'يرجى إدخال مبلغ صحيح');
      return;
    }

    if (paidBy.length === 0) {
      Alert.alert('خطأ', 'يرجى اختيار من دفع المصروف');
      return;
    }

    if (splitBetween.length === 0) {
      Alert.alert('خطأ', 'يرجى اختيار من سيتم تقسيم المصروف عليهم');
      return;
    }

    if (splitType === 'custom' && !validateCustomSplits()) {
      Alert.alert('خطأ', 'مجموع المبالغ المخصصة يجب أن يساوي المبلغ الإجمالي');
      return;
    }

    setLoading(true);
    try {
      const splitInfo: SplitInfo[] = splitBetween.map(userId => {
        const split: SplitInfo = { userId };
        
        if (splitType === 'custom') {
          split.amount = parseFloat(customSplits[userId] || '0');
        }
        
        return split;
      });

      await addExpense(currentTrip.id, {
        description: description.trim(),
        amount: amountNum,
        currency: settings.defaultCurrency,
        date,
        paidBy,
        splitBetween: splitInfo,
        notes: notes.trim() || undefined,
      });
      
      Alert.alert('نجح', 'تم إضافة المصروف بنجاح', [
        { text: 'موافق', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('خطأ', 'فشل في إضافة المصروف');
    } finally {
      setLoading(false);
    }
  };

  const getRemainingAmount = (): number => {
    const totalAmount = parseFloat(amount) || 0;
    let usedAmount = 0;
    
    for (const userId of splitBetween) {
      usedAmount += parseFloat(customSplits[userId] || '0');
    }
    
    return totalAmount - usedAmount;
  };

  return (
    <KeyboardAvoidingView 
      className="flex-1 bg-gray-50 dark:bg-gray-900"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView className="flex-1 p-4">
        {/* Trip Info */}
        <View className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3 mb-6">
          <Text className="text-primary-700 dark:text-primary-300 font-medium text-center">
            إضافة مصروف لرحلة: {currentTrip.name}
          </Text>
        </View>

        {/* Description */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            وصف المصروف *
          </Text>
          <TextInput
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
            placeholder="مثال: فندق، مطعم، مواصلات"
            placeholderTextColor="#9ca3af"
            value={description}
            onChangeText={setDescription}
          />
        </View>

        {/* Amount */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            المبلغ * ({settings.defaultCurrency})
          </Text>
          <TextInput
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
            placeholder="0.00"
            placeholderTextColor="#9ca3af"
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
          />
        </View>

        {/* Date */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            التاريخ *
          </Text>
          <TextInput
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
            placeholder="YYYY-MM-DD"
            placeholderTextColor="#9ca3af"
            value={date}
            onChangeText={setDate}
          />
        </View>

        {/* Paid By */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            من دفع؟ *
          </Text>
          {currentTrip.members.map((member) => (
            <TouchableOpacity
              key={member.id}
              className={`flex-row items-center p-3 mb-2 rounded-lg border ${
                paidBy.includes(member.id)
                  ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-300 dark:border-primary-700'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600'
              }`}
              onPress={() => handleTogglePaidBy(member.id)}
            >
              <Ionicons 
                name={paidBy.includes(member.id) ? 'checkmark-circle' : 'ellipse-outline'} 
                size={24} 
                color={paidBy.includes(member.id) ? '#3b82f6' : '#9ca3af'} 
              />
              <Text className={`ml-3 font-medium ${
                paidBy.includes(member.id)
                  ? 'text-primary-700 dark:text-primary-300'
                  : 'text-gray-900 dark:text-white'
              }`}>
                {member.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Split Type */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            طريقة التقسيم
          </Text>
          <View className="flex-row space-x-2">
            <TouchableOpacity
              className={`flex-1 p-3 rounded-lg border mr-2 ${
                splitType === 'equal'
                  ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-300 dark:border-primary-700'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600'
              }`}
              onPress={() => setSplitType('equal')}
            >
              <Text className={`text-center font-medium ${
                splitType === 'equal'
                  ? 'text-primary-700 dark:text-primary-300'
                  : 'text-gray-900 dark:text-white'
              }`}>
                تقسيم متساوي
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              className={`flex-1 p-3 rounded-lg border ${
                splitType === 'custom'
                  ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-300 dark:border-primary-700'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600'
              }`}
              onPress={() => setSplitType('custom')}
            >
              <Text className={`text-center font-medium ${
                splitType === 'custom'
                  ? 'text-primary-700 dark:text-primary-300'
                  : 'text-gray-900 dark:text-white'
              }`}>
                تقسيم مخصص
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Split Between */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-lg font-semibold text-gray-900 dark:text-white">
              مقسم على؟ *
            </Text>
            <TouchableOpacity
              className="bg-primary-600 px-3 py-1 rounded-full"
              onPress={handleSelectAllMembers}
            >
              <Text className="text-white text-sm font-medium">اختيار الكل</Text>
            </TouchableOpacity>
          </View>
          
          {splitType === 'custom' && amount && (
            <View className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg mb-3">
              <Text className="text-yellow-700 dark:text-yellow-300 text-sm">
                المبلغ المتبقي: {getRemainingAmount().toFixed(2)} {settings.defaultCurrency}
              </Text>
            </View>
          )}
          
          {currentTrip.members.map((member) => (
            <View key={member.id} className="mb-2">
              <TouchableOpacity
                className={`flex-row items-center p-3 rounded-lg border ${
                  splitBetween.includes(member.id)
                    ? 'bg-secondary-50 dark:bg-secondary-900/20 border-secondary-300 dark:border-secondary-700'
                    : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600'
                }`}
                onPress={() => handleToggleSplitBetween(member.id)}
              >
                <Ionicons 
                  name={splitBetween.includes(member.id) ? 'checkmark-circle' : 'ellipse-outline'} 
                  size={24} 
                  color={splitBetween.includes(member.id) ? '#22c55e' : '#9ca3af'} 
                />
                <Text className={`ml-3 font-medium flex-1 ${
                  splitBetween.includes(member.id)
                    ? 'text-secondary-700 dark:text-secondary-300'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {member.name}
                </Text>
                
                {splitType === 'equal' && splitBetween.includes(member.id) && amount && (
                  <Text className="text-gray-600 dark:text-gray-400 text-sm">
                    {(parseFloat(amount) / splitBetween.length).toFixed(2)} {settings.defaultCurrency}
                  </Text>
                )}
              </TouchableOpacity>
              
              {splitType === 'custom' && splitBetween.includes(member.id) && (
                <TextInput
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 mt-2 text-gray-900 dark:text-white"
                  placeholder={`مبلغ ${member.name}`}
                  placeholderTextColor="#9ca3af"
                  value={customSplits[member.id] || ''}
                  onChangeText={(value) => handleCustomSplitChange(member.id, value)}
                  keyboardType="numeric"
                />
              )}
            </View>
          ))}
        </View>

        {/* Notes */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            ملاحظات (اختياري)
          </Text>
          <TextInput
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white"
            placeholder="أي ملاحظات إضافية..."
            placeholderTextColor="#9ca3af"
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Save Button */}
        <TouchableOpacity
          className={`py-4 rounded-lg items-center mb-8 ${
            loading 
              ? 'bg-gray-400' 
              : 'bg-primary-600'
          }`}
          onPress={handleSaveExpense}
          disabled={loading}
        >
          <Text className="text-white font-semibold text-lg">
            {loading ? 'جاري الحفظ...' : 'إضافة المصروف'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
