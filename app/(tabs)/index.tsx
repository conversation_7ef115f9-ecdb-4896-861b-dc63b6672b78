import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { Trip } from '../../types';
import { formatDate, formatCurrency } from '../../utils/calculations';

export default function TripsScreen() {
  const { trips, loading, deleteTrip, initializeApp } = useStore();

  const handleDeleteTrip = (trip: Trip) => {
    Alert.alert(
      'حذف الرحلة',
      `هل أنت متأكد من حذف رحلة "${trip.name}"؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: () => deleteTrip(trip.id),
        },
      ]
    );
  };

  const getTripTotal = (trip: Trip): number => {
    return trip.expenses.reduce((total, expense) => total + expense.amount, 0);
  };

  const renderTripCard = ({ item: trip }: { item: Trip }) => (
    <TouchableOpacity
      className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 mx-4 shadow-sm border border-gray-200 dark:border-gray-700"
      onPress={() => router.push(`/trip/${trip.id}`)}
    >
      <View className="flex-row justify-between items-start mb-2">
        <Text className="text-lg font-bold text-gray-900 dark:text-white flex-1">
          {trip.name}
        </Text>
        <TouchableOpacity
          onPress={() => handleDeleteTrip(trip)}
          className="p-1"
        >
          <Ionicons name="trash-outline" size={20} color="#ef4444" />
        </TouchableOpacity>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="calendar-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
        </Text>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="people-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          {trip.members.length} أشخاص
        </Text>
      </View>
      
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center">
          <Ionicons name="receipt-outline" size={16} color="#6b7280" />
          <Text className="text-gray-600 dark:text-gray-400 ml-2">
            {trip.expenses.length} مصروف
          </Text>
        </View>
        <Text className="text-lg font-bold text-primary-600">
          {formatCurrency(getTripTotal(trip))}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const EmptyState = () => (
    <View className="flex-1 justify-center items-center px-8">
      <Ionicons name="airplane-outline" size={80} color="#9ca3af" />
      <Text className="text-xl font-bold text-gray-500 dark:text-gray-400 mt-4 text-center">
        لا توجد رحلات بعد
      </Text>
      <Text className="text-gray-400 dark:text-gray-500 mt-2 text-center">
        ابدأ بإنشاء رحلتك الأولى لتتبع المصروفات
      </Text>
      <TouchableOpacity
        className="bg-primary-600 px-6 py-3 rounded-lg mt-6"
        onPress={() => router.push('/trip/add')}
      >
        <Text className="text-white font-semibold">إنشاء رحلة جديدة</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      {trips.length === 0 ? (
        <EmptyState />
      ) : (
        <FlatList
          data={trips}
          renderItem={renderTripCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingVertical: 16 }}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={initializeApp}
              tintColor="#3b82f6"
            />
          }
        />
      )}
      
      {trips.length > 0 && (
        <TouchableOpacity
          className="absolute bottom-6 right-6 bg-primary-600 w-14 h-14 rounded-full justify-center items-center shadow-lg"
          onPress={() => router.push('/trip/add')}
        >
          <Ionicons name="add" size={28} color="white" />
        </TouchableOpacity>
      )}
    </View>
  );
}
