import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { Expense, Trip } from '../../types';
import { formatDate, formatCurrency } from '../../utils/calculations';

export default function ExpensesScreen() {
  const { trips, loading, initializeApp } = useStore();
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);

  // Get all expenses from all trips or from selected trip
  const getAllExpenses = (): (Expense & { tripName: string })[] => {
    const allExpenses: (Expense & { tripName: string })[] = [];
    
    trips.forEach(trip => {
      if (!selectedTripId || trip.id === selectedTripId) {
        trip.expenses.forEach(expense => {
          allExpenses.push({
            ...expense,
            tripName: trip.name,
          });
        });
      }
    });
    
    return allExpenses.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const expenses = getAllExpenses();

  const getUserName = (userId: string): string => {
    for (const trip of trips) {
      const user = trip.members.find(member => member.id === userId);
      if (user) return user.name;
    }
    return 'مستخدم غير معروف';
  };

  const renderExpenseCard = ({ item: expense }: { item: Expense & { tripName: string } }) => (
    <View className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 mx-4 shadow-sm border border-gray-200 dark:border-gray-700">
      <View className="flex-row justify-between items-start mb-2">
        <Text className="text-lg font-semibold text-gray-900 dark:text-white flex-1">
          {expense.description}
        </Text>
        <Text className="text-lg font-bold text-primary-600">
          {formatCurrency(expense.amount, expense.currency)}
        </Text>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="airplane-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          {expense.tripName}
        </Text>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="calendar-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          {formatDate(expense.date)}
        </Text>
      </View>
      
      <View className="flex-row items-center mb-2">
        <Ionicons name="person-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          دفع: {expense.paidBy.map(id => getUserName(id)).join(', ')}
        </Text>
      </View>
      
      <View className="flex-row items-center">
        <Ionicons name="people-outline" size={16} color="#6b7280" />
        <Text className="text-gray-600 dark:text-gray-400 ml-2">
          مقسم على: {expense.splitBetween.length} أشخاص
        </Text>
      </View>
      
      {expense.notes && (
        <View className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          <Text className="text-gray-600 dark:text-gray-400 text-sm">
            {expense.notes}
          </Text>
        </View>
      )}
    </View>
  );

  const TripFilter = () => (
    <View className="px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={[{ id: null, name: 'جميع الرحلات' }, ...trips]}
        keyExtractor={(item) => item.id || 'all'}
        renderItem={({ item }) => (
          <TouchableOpacity
            className={`px-4 py-2 rounded-full mr-2 ${
              selectedTripId === item.id
                ? 'bg-primary-600'
                : 'bg-gray-200 dark:bg-gray-700'
            }`}
            onPress={() => setSelectedTripId(item.id)}
          >
            <Text
              className={`font-medium ${
                selectedTripId === item.id
                  ? 'text-white'
                  : 'text-gray-700 dark:text-gray-300'
              }`}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const EmptyState = () => (
    <View className="flex-1 justify-center items-center px-8">
      <Ionicons name="receipt-outline" size={80} color="#9ca3af" />
      <Text className="text-xl font-bold text-gray-500 dark:text-gray-400 mt-4 text-center">
        لا توجد مصروفات
      </Text>
      <Text className="text-gray-400 dark:text-gray-500 mt-2 text-center">
        {selectedTripId 
          ? 'لا توجد مصروفات في هذه الرحلة بعد'
          : 'ابدأ بإضافة مصروفات لرحلاتك'
        }
      </Text>
    </View>
  );

  const getTotalExpenses = (): number => {
    return expenses.reduce((total, expense) => total + expense.amount, 0);
  };

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      {trips.length > 0 && <TripFilter />}
      
      {expenses.length > 0 && (
        <View className="px-4 py-3 bg-primary-50 dark:bg-primary-900/20 border-b border-primary-200 dark:border-primary-800">
          <Text className="text-center text-lg font-bold text-primary-700 dark:text-primary-300">
            إجمالي المصروفات: {formatCurrency(getTotalExpenses())}
          </Text>
          <Text className="text-center text-sm text-primary-600 dark:text-primary-400 mt-1">
            {expenses.length} مصروف
          </Text>
        </View>
      )}
      
      {expenses.length === 0 ? (
        <EmptyState />
      ) : (
        <FlatList
          data={expenses}
          renderItem={renderExpenseCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingVertical: 16 }}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={initializeApp}
              tintColor="#3b82f6"
            />
          }
        />
      )}
    </View>
  );
}
