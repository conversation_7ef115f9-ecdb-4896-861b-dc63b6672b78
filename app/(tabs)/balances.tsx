import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { formatCurrency } from '../../utils/calculations';

export default function BalancesScreen() {
  const { trips, loading, initializeApp, getTripBalances, getTripSettlements } = useStore();
  const [selectedTripId, setSelectedTripId] = useState<string | null>(
    trips.length > 0 ? trips[0].id : null
  );

  const selectedTrip = trips.find(trip => trip.id === selectedTripId);
  const balances = selectedTripId ? getTripBalances(selectedTripId) : [];
  const settlements = selectedTripId ? getTripSettlements(selectedTripId) : [];

  const renderBalanceCard = ({ item: balance }: { item: any }) => (
    <View className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 mx-4 shadow-sm border border-gray-200 dark:border-gray-700">
      <View className="flex-row justify-between items-center mb-3">
        <Text className="text-lg font-semibold text-gray-900 dark:text-white">
          {balance.userName}
        </Text>
        <View className={`px-3 py-1 rounded-full ${
          balance.netBalance > 0 
            ? 'bg-secondary-100 dark:bg-secondary-900/30' 
            : balance.netBalance < 0
            ? 'bg-danger-100 dark:bg-danger-900/30'
            : 'bg-gray-100 dark:bg-gray-700'
        }`}>
          <Text className={`font-bold ${
            balance.netBalance > 0 
              ? 'text-secondary-700 dark:text-secondary-300' 
              : balance.netBalance < 0
              ? 'text-danger-700 dark:text-danger-300'
              : 'text-gray-700 dark:text-gray-300'
          }`}>
            {balance.netBalance > 0 ? '+' : ''}{formatCurrency(balance.netBalance)}
          </Text>
        </View>
      </View>
      
      <View className="space-y-2">
        <View className="flex-row justify-between">
          <Text className="text-gray-600 dark:text-gray-400">دفع:</Text>
          <Text className="text-gray-900 dark:text-white font-medium">
            {formatCurrency(balance.totalPaid)}
          </Text>
        </View>
        <View className="flex-row justify-between">
          <Text className="text-gray-600 dark:text-gray-400">يدين بـ:</Text>
          <Text className="text-gray-900 dark:text-white font-medium">
            {formatCurrency(balance.totalOwed)}
          </Text>
        </View>
      </View>
      
      {balance.netBalance !== 0 && (
        <View className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <Text className={`text-center font-medium ${
            balance.netBalance > 0 
              ? 'text-secondary-600 dark:text-secondary-400' 
              : 'text-danger-600 dark:text-danger-400'
          }`}>
            {balance.netBalance > 0 
              ? `يستحق ${formatCurrency(balance.netBalance)}`
              : `يدين بـ ${formatCurrency(Math.abs(balance.netBalance))}`
            }
          </Text>
        </View>
      )}
    </View>
  );

  const renderSettlementCard = ({ item: settlement }: { item: any }) => (
    <View className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 mx-4 shadow-sm border border-gray-200 dark:border-gray-700">
      <View className="flex-row items-center justify-between">
        <View className="flex-1">
          <Text className="text-lg font-semibold text-gray-900 dark:text-white">
            {settlement.fromName}
          </Text>
          <View className="flex-row items-center mt-1">
            <Ionicons name="arrow-forward" size={16} color="#6b7280" />
            <Text className="text-gray-600 dark:text-gray-400 ml-2">
              {settlement.toName}
            </Text>
          </View>
        </View>
        <Text className="text-lg font-bold text-primary-600">
          {formatCurrency(settlement.amount)}
        </Text>
      </View>
    </View>
  );

  const TripSelector = () => (
    <View className="px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        اختر الرحلة:
      </Text>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={trips}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            className={`px-4 py-2 rounded-full mr-2 ${
              selectedTripId === item.id
                ? 'bg-primary-600'
                : 'bg-gray-200 dark:bg-gray-700'
            }`}
            onPress={() => setSelectedTripId(item.id)}
          >
            <Text
              className={`font-medium ${
                selectedTripId === item.id
                  ? 'text-white'
                  : 'text-gray-700 dark:text-gray-300'
              }`}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const EmptyState = () => (
    <View className="flex-1 justify-center items-center px-8">
      <Ionicons name="analytics-outline" size={80} color="#9ca3af" />
      <Text className="text-xl font-bold text-gray-500 dark:text-gray-400 mt-4 text-center">
        لا توجد رحلات
      </Text>
      <Text className="text-gray-400 dark:text-gray-500 mt-2 text-center">
        أنشئ رحلة وأضف مصروفات لرؤية الأرصدة
      </Text>
    </View>
  );

  if (trips.length === 0) {
    return (
      <View className="flex-1 bg-gray-50 dark:bg-gray-900">
        <EmptyState />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <TripSelector />
      
      {selectedTrip && (
        <FlatList
          data={[]}
          ListHeaderComponent={
            <View>
              {/* Balances Section */}
              <View className="px-4 py-3 bg-primary-50 dark:bg-primary-900/20">
                <Text className="text-lg font-bold text-primary-700 dark:text-primary-300">
                  أرصدة الأعضاء
                </Text>
              </View>
              
              <FlatList
                data={balances}
                renderItem={renderBalanceCard}
                keyExtractor={(item) => item.userId}
                scrollEnabled={false}
                contentContainerStyle={{ paddingVertical: 8 }}
              />
              
              {/* Settlements Section */}
              {settlements.length > 0 && (
                <>
                  <View className="px-4 py-3 bg-secondary-50 dark:bg-secondary-900/20 mt-4">
                    <Text className="text-lg font-bold text-secondary-700 dark:text-secondary-300">
                      اقتراحات التسوية
                    </Text>
                    <Text className="text-sm text-secondary-600 dark:text-secondary-400 mt-1">
                      أقل عدد من التحويلات المطلوبة لتسوية جميع الديون
                    </Text>
                  </View>
                  
                  <FlatList
                    data={settlements}
                    renderItem={renderSettlementCard}
                    keyExtractor={(item, index) => `${item.from}-${item.to}-${index}`}
                    scrollEnabled={false}
                    contentContainerStyle={{ paddingVertical: 8 }}
                  />
                </>
              )}
              
              {balances.length === 0 && (
                <View className="flex-1 justify-center items-center py-16">
                  <Ionicons name="calculator-outline" size={60} color="#9ca3af" />
                  <Text className="text-lg font-medium text-gray-500 dark:text-gray-400 mt-4 text-center">
                    لا توجد مصروفات في هذه الرحلة
                  </Text>
                  <Text className="text-gray-400 dark:text-gray-500 mt-2 text-center">
                    أضف مصروفات لرؤية الأرصدة والتسويات
                  </Text>
                </View>
              )}
            </View>
          }
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={initializeApp}
              tintColor="#3b82f6"
            />
          }
        />
      )}
    </View>
  );
}
