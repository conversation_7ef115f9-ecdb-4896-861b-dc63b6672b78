import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
  useColorScheme,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useStore } from '../../store/useStore';
import { clearAllData } from '../../utils/storage';

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const { settings, updateSettings, trips } = useStore();

  const handleClearData = () => {
    Alert.alert(
      'مسح جميع البيانات',
      'هل أنت متأكد من مسح جميع الرحلات والمصروفات؟ لا يمكن التراجع عن هذا الإجراء.',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'مسح',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearAllData();
              // Reload the app
              window.location.reload();
            } catch (error) {
              Alert.alert('خطأ', 'فشل في مسح البيانات');
            }
          },
        },
      ]
    );
  };

  const handleLanguageChange = (language: 'ar' | 'en') => {
    updateSettings({ language });
  };

  const handleCurrencyChange = (currency: string) => {
    updateSettings({ defaultCurrency: currency });
  };

  const currencies = [
    { code: 'EGP', name: 'جنيه مصري', symbol: 'ج.م' },
    { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
    { code: 'EUR', name: 'يورو', symbol: '€' },
    { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
    { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
  ];

  const SettingItem = ({ 
    icon, 
    title, 
    subtitle, 
    onPress, 
    rightElement 
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity
      className="bg-white dark:bg-gray-800 px-4 py-4 border-b border-gray-200 dark:border-gray-700"
      onPress={onPress}
      disabled={!onPress}
    >
      <View className="flex-row items-center">
        <View className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-full justify-center items-center mr-3">
          <Ionicons name={icon as any} size={20} color="#3b82f6" />
        </View>
        <View className="flex-1">
          <Text className="text-lg font-medium text-gray-900 dark:text-white">
            {title}
          </Text>
          {subtitle && (
            <Text className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {subtitle}
            </Text>
          )}
        </View>
        {rightElement || (onPress && (
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        ))}
      </View>
    </TouchableOpacity>
  );

  const StatCard = ({ icon, title, value, color }: {
    icon: string;
    title: string;
    value: string | number;
    color: string;
  }) => (
    <View className="bg-white dark:bg-gray-800 rounded-lg p-4 flex-1 mx-2 shadow-sm border border-gray-200 dark:border-gray-700">
      <View className="flex-row items-center mb-2">
        <Ionicons name={icon as any} size={24} color={color} />
        <Text className="text-sm font-medium text-gray-600 dark:text-gray-400 ml-2">
          {title}
        </Text>
      </View>
      <Text className="text-2xl font-bold text-gray-900 dark:text-white">
        {value}
      </Text>
    </View>
  );

  const totalTrips = trips.length;
  const totalExpenses = trips.reduce((sum, trip) => sum + trip.expenses.length, 0);
  const totalAmount = trips.reduce((sum, trip) => 
    sum + trip.expenses.reduce((expSum, exp) => expSum + exp.amount, 0), 0
  );

  return (
    <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
      {/* Stats Section */}
      <View className="px-4 py-6">
        <Text className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          إحصائياتك
        </Text>
        <View className="flex-row">
          <StatCard
            icon="airplane"
            title="الرحلات"
            value={totalTrips}
            color="#3b82f6"
          />
          <StatCard
            icon="receipt"
            title="المصروفات"
            value={totalExpenses}
            color="#22c55e"
          />
        </View>
        <View className="mt-4">
          <View className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <View className="flex-row items-center mb-2">
              <Ionicons name="cash" size={24} color="#f59e0b" />
              <Text className="text-sm font-medium text-gray-600 dark:text-gray-400 ml-2">
                إجمالي المبلغ
              </Text>
            </View>
            <Text className="text-2xl font-bold text-gray-900 dark:text-white">
              {totalAmount.toFixed(2)} {settings.defaultCurrency}
            </Text>
          </View>
        </View>
      </View>

      {/* Settings Section */}
      <View className="mb-6">
        <Text className="text-xl font-bold text-gray-900 dark:text-white px-4 mb-4">
          الإعدادات
        </Text>
        
        <View className="bg-white dark:bg-gray-800 rounded-lg mx-4 overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700">
          <SettingItem
            icon="language"
            title="اللغة"
            subtitle={settings.language === 'ar' ? 'العربية' : 'English'}
            onPress={() => {
              Alert.alert(
                'اختر اللغة',
                '',
                [
                  { text: 'العربية', onPress: () => handleLanguageChange('ar') },
                  { text: 'English', onPress: () => handleLanguageChange('en') },
                  { text: 'إلغاء', style: 'cancel' },
                ]
              );
            }}
          />
          
          <SettingItem
            icon="card"
            title="العملة الافتراضية"
            subtitle={currencies.find(c => c.code === settings.defaultCurrency)?.name}
            onPress={() => {
              Alert.alert(
                'اختر العملة',
                '',
                [
                  ...currencies.map(currency => ({
                    text: `${currency.name} (${currency.symbol})`,
                    onPress: () => handleCurrencyChange(currency.code),
                  })),
                  { text: 'إلغاء', style: 'cancel' },
                ]
              );
            }}
          />
        </View>
      </View>

      {/* App Info Section */}
      <View className="mb-6">
        <Text className="text-xl font-bold text-gray-900 dark:text-white px-4 mb-4">
          معلومات التطبيق
        </Text>
        
        <View className="bg-white dark:bg-gray-800 rounded-lg mx-4 overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700">
          <SettingItem
            icon="information-circle"
            title="الإصدار"
            subtitle="1.0.0"
          />
          
          <SettingItem
            icon="help-circle"
            title="المساعدة والدعم"
            onPress={() => {
              Alert.alert(
                'المساعدة والدعم',
                'TripSplit - تطبيق لإدارة مصروفات الرحلات\n\nللمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل معنا.'
              );
            }}
          />
        </View>
      </View>

      {/* Danger Zone */}
      <View className="mb-8">
        <Text className="text-xl font-bold text-danger-600 dark:text-danger-400 px-4 mb-4">
          منطقة الخطر
        </Text>
        
        <View className="bg-white dark:bg-gray-800 rounded-lg mx-4 overflow-hidden shadow-sm border border-danger-200 dark:border-danger-800">
          <SettingItem
            icon="trash"
            title="مسح جميع البيانات"
            subtitle="حذف جميع الرحلات والمصروفات نهائياً"
            onPress={handleClearData}
          />
        </View>
      </View>
    </ScrollView>
  );
}
