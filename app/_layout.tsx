import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useColorScheme } from 'react-native';
import { useStore } from '../store/useStore';
import '../global.css';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const initializeApp = useStore((state) => state.initializeApp);

  useEffect(() => {
    initializeApp();
  }, []);

  return (
    <>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff',
          },
          headerTintColor: colorScheme === 'dark' ? '#ffffff' : '#000000',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen
          name="trip/[id]"
          options={{
            title: 'تفاصيل الرحلة',
            presentation: 'card'
          }}
        />
        <Stack.Screen
          name="expense/add"
          options={{
            title: 'إضافة مصروف',
            presentation: 'modal'
          }}
        />
        <Stack.Screen
          name="expense/edit/[id]"
          options={{
            title: 'تعديل المصروف',
            presentation: 'modal'
          }}
        />
        <Stack.Screen
          name="trip/add"
          options={{
            title: 'رحلة جديدة',
            presentation: 'modal'
          }}
        />
        <Stack.Screen
          name="trip/edit/[id]"
          options={{
            title: 'تعديل الرحلة',
            presentation: 'modal'
          }}
        />
      </Stack>
    </>
  );
}
