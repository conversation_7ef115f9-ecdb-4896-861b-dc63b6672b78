import React, { useState } from 'react';
import { ScrollView, View, Text, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Button, Card, Input } from './components';
import { useStore } from './store/useStore';
import { formatCurrency } from './utils/calculations';

import './global.css';

export default function App() {
  const [inputValue, setInputValue] = useState('');
  const [inputError, setInputError] = useState('');
  const [loading, setLoading] = useState(false);

  const { initializeApp, trips, loading: storeLoading } = useStore();

  React.useEffect(() => {
    initializeApp();
  }, []);

  const handleButtonPress = () => {
    Alert.alert('Button Pressed!', 'This is a demo of the Button component from EL-REHLAH');
  };

  const handleLoadingButton = async () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      Alert.alert('Loading Complete!', 'This demonstrates the loading state');
    }, 2000);
  };

  const validateInput = (value: string) => {
    if (value.length < 3) {
      setInputError('Input must be at least 3 characters');
    } else {
      setInputError('');
    }
  };

  return (
    <>
      <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
        <View className="p-4">
          <Text className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-6">
            EL-REHLAH Components Demo
          </Text>

          <Text className="text-lg text-gray-600 dark:text-gray-300 text-center mb-8">
            Showcasing reusable components from the TripSplit app
          </Text>

          {/* Button Components Section */}
          <Card className="mb-6">
            <Text className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Button Components
            </Text>

            <View className="space-y-3">
              <Button
                title="Primary Button"
                onPress={handleButtonPress}
                variant="primary"
              />

              <Button
                title="Secondary Button"
                onPress={handleButtonPress}
                variant="secondary"
              />

              <Button
                title="Danger Button"
                onPress={handleButtonPress}
                variant="danger"
              />

              <Button
                title="Outline Button"
                onPress={handleButtonPress}
                variant="outline"
              />

              <Button
                title="With Icon"
                onPress={handleButtonPress}
                icon="add-circle-outline"
                variant="primary"
              />

              <Button
                title="Loading Button"
                onPress={handleLoadingButton}
                loading={loading}
                variant="primary"
              />

              <View className="flex-row space-x-2">
                <Button
                  title="Small"
                  onPress={handleButtonPress}
                  size="sm"
                  className="flex-1"
                />
                <Button
                  title="Medium"
                  onPress={handleButtonPress}
                  size="md"
                  className="flex-1"
                />
                <Button
                  title="Large"
                  onPress={handleButtonPress}
                  size="lg"
                  className="flex-1"
                />
              </View>
            </View>
          </Card>

          {/* Input Components Section */}
          <Card className="mb-6">
            <Text className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Input Components
            </Text>

            <Input
              label="Basic Input"
              placeholder="Enter some text..."
              value={inputValue}
              onChangeText={(text) => {
                setInputValue(text);
                validateInput(text);
              }}
              error={inputError}
            />

            <Input
              label="Input with Icon"
              placeholder="Search..."
              icon="search-outline"
            />

            <Input
              label="Required Field"
              placeholder="This field is required"
              required
            />

            <Input
              label="Email Input"
              placeholder="Enter your email"
              icon="mail-outline"
              keyboardType="email-address"
            />
          </Card>

          {/* Card Components Section */}
          <Card className="mb-6">
            <Text className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Card Variants
            </Text>

            <Card variant="default" className="mb-3">
              <Text className="text-gray-900 dark:text-white">
                Default Card - Standard shadow and border
              </Text>
            </Card>

            <Card variant="elevated" className="mb-3">
              <Text className="text-gray-900 dark:text-white">
                Elevated Card - Enhanced shadow for prominence
              </Text>
            </Card>

            <Card variant="outlined" className="mb-3">
              <Text className="text-gray-900 dark:text-white">
                Outlined Card - Bold border, no shadow
              </Text>
            </Card>

            <Card padding="sm" className="mb-3">
              <Text className="text-gray-900 dark:text-white">
                Small Padding Card
              </Text>
            </Card>

            <Card padding="lg">
              <Text className="text-gray-900 dark:text-white">
                Large Padding Card - More spacious layout
              </Text>
            </Card>
          </Card>

          {/* Utility Functions Section */}
          <Card className="mb-6">
            <Text className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Utility Functions Demo
            </Text>

            <View className="space-y-2">
              <Text className="text-gray-900 dark:text-white">
                Currency Formatting:
              </Text>
              <Text className="text-gray-600 dark:text-gray-300 ml-4">
                • {formatCurrency(1500, 'EGP')}
              </Text>
              <Text className="text-gray-600 dark:text-gray-300 ml-4">
                • {formatCurrency(250.75, 'USD')}
              </Text>
              <Text className="text-gray-600 dark:text-gray-300 ml-4">
                • {formatCurrency(89.99, 'EUR')}
              </Text>
            </View>
          </Card>

          {/* Store Integration Section */}
          <Card className="mb-6">
            <Text className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Zustand Store Integration
            </Text>

            <Text className="text-gray-600 dark:text-gray-300 mb-2">
              Store Status: {storeLoading ? 'Loading...' : 'Ready'}
            </Text>
            <Text className="text-gray-600 dark:text-gray-300">
              Trips in Store: {trips.length}
            </Text>

            <Button
              title="Test Store Function"
              onPress={() => Alert.alert('Store Ready!', `The Zustand store is working with ${trips.length} trips loaded.`)}
              variant="outline"
              className="mt-4"
            />
          </Card>

          {/* Features Summary */}
          <Card>
            <Text className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Available Features from EL-REHLAH
            </Text>

            <View className="space-y-2">
              <Text className="text-gray-600 dark:text-gray-300">✅ Button component with variants, sizes, icons, and loading states</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ Card component with different styles and padding options</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ Input component with labels, icons, and error handling</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ TypeScript interfaces for Trip, Expense, User management</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ Zustand store for state management</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ AsyncStorage utilities for data persistence</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ Calculation utilities for balances and settlements</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ Currency formatting and date utilities</Text>
              <Text className="text-gray-600 dark:text-gray-300">✅ Dark mode support throughout all components</Text>
            </View>
          </Card>
        </View>
      </ScrollView>
      <StatusBar style="auto" />
    </>
  );
}


