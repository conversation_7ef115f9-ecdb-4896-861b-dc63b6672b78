import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { useTripStore } from './store/tripStore';
import { LoadingSpinner } from './components';
import { MainTabNavigator } from './navigation/MainTabNavigator';
import { TripDetailsScreen } from './screens/TripDetailsScreen';
import { AddTripScreen } from './screens/AddTripScreen';
import { EditTripScreen } from './screens/EditTripScreen';
import { AddExpenseScreen } from './screens/AddExpenseScreen';
import { EditExpenseScreen } from './screens/EditExpenseScreen';
import { SettingsScreen } from './screens/SettingsScreen';
import { RootStackParamList } from './types';

import './global.css';

const Stack = createStackNavigator<RootStackParamList>();

export default function App() {
  const { initializeApp, loading } = useTripStore();

  useEffect(() => {
    initializeApp();
  }, []);

  if (loading) {
    return <LoadingSpinner message="Initializing TripSplit..." />;
  }

  return (
    <>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Main"
          screenOptions={{
            headerStyle: {
              backgroundColor: '#2563eb',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}
        >
          <Stack.Screen
            name="Main"
            component={MainTabNavigator}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="TripDetails"
            component={TripDetailsScreen}
            options={{ title: 'Trip Details' }}
          />
          <Stack.Screen
            name="AddTrip"
            component={AddTripScreen}
            options={{ title: 'New Trip' }}
          />
          <Stack.Screen
            name="EditTrip"
            component={EditTripScreen}
            options={{ title: 'Edit Trip' }}
          />
          <Stack.Screen
            name="AddExpense"
            component={AddExpenseScreen}
            options={{ title: 'Add Expense' }}
          />
          <Stack.Screen
            name="EditExpense"
            component={EditExpenseScreen}
            options={{ title: 'Edit Expense' }}
          />
          <Stack.Screen
            name="Settings"
            component={SettingsScreen}
            options={{ title: 'Settings' }}
          />
        </Stack.Navigator>
      </NavigationContainer>
      <StatusBar style="light" />
    </>
  );
}


