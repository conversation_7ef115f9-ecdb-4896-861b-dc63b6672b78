{"name": "my-expo-app", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"expo": "^53.0.9", "expo-status-bar": "~2.2.3", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}}