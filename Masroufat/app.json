{"expo": {"name": "TripSplit", "slug": "tripsplit", "version": "1.0.0", "sdkVersion": "53.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#2563eb"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#2563eb"}}, "web": {"favicon": "./assets/favicon.png"}, "experiments": {"tsconfigPaths": true}, "plugins": ["nativewind/babel"]}}