import { Trip, Balance, Settlement, Expense } from '../types';

export const calculateBalances = (trip: Trip): Balance[] => {
  const balances: { [userId: string]: Balance } = {};
  
  // Initialize balances for all members
  trip.members.forEach(member => {
    balances[member.id] = {
      userId: member.id,
      userName: member.name,
      totalPaid: 0,
      totalOwed: 0,
      netBalance: 0,
    };
  });

  // Calculate totals from expenses
  trip.expenses.forEach(expense => {
    // Calculate how much each person paid
    const paidPerPerson = expense.amount / expense.paidBy.length;
    expense.paidBy.forEach(userId => {
      if (balances[userId]) {
        balances[userId].totalPaid += paidPerPerson;
      }
    });

    // Calculate how much each person owes
    const splitAmount = calculateSplitAmounts(expense);
    Object.entries(splitAmount).forEach(([userId, amount]) => {
      if (balances[userId]) {
        balances[userId].totalOwed += amount;
      }
    });
  });

  // Calculate net balances
  Object.values(balances).forEach(balance => {
    balance.netBalance = balance.totalPaid - balance.totalOwed;
  });

  return Object.values(balances);
};

export const calculateSplitAmounts = (expense: Expense): { [userId: string]: number } => {
  const splitAmounts: { [userId: string]: number } = {};
  
  if (expense.splitBetween.length === 0) {
    return splitAmounts;
  }

  // Check if custom amounts are specified
  const hasCustomAmounts = expense.splitBetween.some(split => split.amount !== undefined);
  const hasPercentages = expense.splitBetween.some(split => split.percentage !== undefined);

  if (hasCustomAmounts) {
    // Custom amount split
    expense.splitBetween.forEach(split => {
      if (split.amount !== undefined) {
        splitAmounts[split.userId] = split.amount;
      }
    });
  } else if (hasPercentages) {
    // Percentage split
    expense.splitBetween.forEach(split => {
      if (split.percentage !== undefined) {
        splitAmounts[split.userId] = (expense.amount * split.percentage) / 100;
      }
    });
  } else {
    // Equal split (default)
    const amountPerPerson = expense.amount / expense.splitBetween.length;
    expense.splitBetween.forEach(split => {
      splitAmounts[split.userId] = amountPerPerson;
    });
  }

  return splitAmounts;
};

export const calculateSettlements = (balances: Balance[]): Settlement[] => {
  const settlements: Settlement[] = [];
  
  // Separate creditors (positive balance) and debtors (negative balance)
  const creditors = balances.filter(b => b.netBalance > 0.01).sort((a, b) => b.netBalance - a.netBalance);
  const debtors = balances.filter(b => b.netBalance < -0.01).sort((a, b) => a.netBalance - b.netBalance);
  
  // Create copies to avoid mutating original data
  const creditorsQueue = creditors.map(c => ({ ...c }));
  const debtorsQueue = debtors.map(d => ({ ...d }));
  
  while (creditorsQueue.length > 0 && debtorsQueue.length > 0) {
    const creditor = creditorsQueue[0];
    const debtor = debtorsQueue[0];
    
    const settlementAmount = Math.min(creditor.netBalance, Math.abs(debtor.netBalance));
    
    if (settlementAmount > 0.01) {
      settlements.push({
        from: debtor.userId,
        to: creditor.userId,
        amount: settlementAmount,
        fromName: debtor.userName,
        toName: creditor.userName,
      });
    }
    
    creditor.netBalance -= settlementAmount;
    debtor.netBalance += settlementAmount;
    
    // Remove settled parties
    if (Math.abs(creditor.netBalance) < 0.01) {
      creditorsQueue.shift();
    }
    if (Math.abs(debtor.netBalance) < 0.01) {
      debtorsQueue.shift();
    }
  }
  
  return settlements;
};

export const formatCurrency = (amount: number, currency: string = 'EGP'): string => {
  const currencySymbols: { [key: string]: string } = {
    EGP: 'ج.م',
    USD: '$',
    EUR: '€',
    SAR: 'ر.س',
    AED: 'د.إ',
  };
  
  const symbol = currencySymbols[currency] || currency;
  return `${amount.toFixed(2)} ${symbol}`;
};

export const formatDate = (dateString: string, locale: string = 'ar'): string => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  
  return date.toLocaleDateString(locale === 'ar' ? 'ar-EG' : 'en-US', options);
};

export const generateTripId = (): string => {
  return `trip_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const generateExpenseId = (): string => {
  return `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const generateUserId = (): string => {
  return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
