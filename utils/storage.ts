import AsyncStorage from '@react-native-async-storage/async-storage';
import { Trip, AppSettings } from '../types';

const TRIPS_KEY = '@tripsplit_trips';
const SETTINGS_KEY = '@tripsplit_settings';

export const saveTrips = async (trips: Trip[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(TRIPS_KEY, JSON.stringify(trips));
  } catch (error) {
    console.error('Failed to save trips:', error);
    throw error;
  }
};

export const loadTrips = async (): Promise<Trip[]> => {
  try {
    const tripsJson = await AsyncStorage.getItem(TRIPS_KEY);
    if (tripsJson) {
      return JSON.parse(tripsJson);
    }
    return [];
  } catch (error) {
    console.error('Failed to load trips:', error);
    return [];
  }
};

export const saveSettings = async (settings: AppSettings): Promise<void> => {
  try {
    await AsyncStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Failed to save settings:', error);
    throw error;
  }
};

export const loadSettings = async (): Promise<Partial<AppSettings>> => {
  try {
    const settingsJson = await AsyncStorage.getItem(SETTINGS_KEY);
    if (settingsJson) {
      return JSON.parse(settingsJson);
    }
    return {};
  } catch (error) {
    console.error('Failed to load settings:', error);
    return {};
  }
};

export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([TRIPS_KEY, SETTINGS_KEY]);
  } catch (error) {
    console.error('Failed to clear data:', error);
    throw error;
  }
};
