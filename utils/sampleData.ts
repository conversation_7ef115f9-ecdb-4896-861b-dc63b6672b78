import { Trip, User, Expense } from '../types';

export const sampleUsers: User[] = [
  { id: 'user_001', name: 'محمد أحمد' },
  { id: 'user_002', name: 'علي حسن' },
  { id: 'user_003', name: 'خا<PERSON><PERSON> محمود' },
  { id: 'user_004', name: 'أحمد علي' },
];

export const sampleTrips: Trip[] = [
  {
    id: 'trip_001',
    name: 'رحلة الغردقة',
    startDate: '2024-04-01',
    endDate: '2024-04-05',
    members: sampleUsers.slice(0, 3),
    expenses: [
      {
        id: 'exp_001',
        tripId: 'trip_001',
        description: 'فندق البحر الأحمر',
        amount: 1500,
        currency: 'EGP',
        date: '2024-04-01',
        paidBy: ['user_001'],
        splitBetween: [
          { userId: 'user_001' },
          { userId: 'user_002' },
          { userId: 'user_003' }
        ],
        category: 'accommodation',
        createdAt: '2024-04-01T10:00:00Z',
        updatedAt: '2024-04-01T10:00:00Z',
      },
      {
        id: 'exp_002',
        tripId: 'trip_001',
        description: 'مطعم السمك',
        amount: 450,
        currency: 'EGP',
        date: '2024-04-02',
        paidBy: ['user_002'],
        splitBetween: [
          { userId: 'user_001' },
          { userId: 'user_002' },
          { userId: 'user_003' }
        ],
        category: 'food',
        createdAt: '2024-04-02T19:30:00Z',
        updatedAt: '2024-04-02T19:30:00Z',
      },
      {
        id: 'exp_003',
        tripId: 'trip_001',
        description: 'رحلة الغطس',
        amount: 600,
        currency: 'EGP',
        date: '2024-04-03',
        paidBy: ['user_003'],
        splitBetween: [
          { userId: 'user_001' },
          { userId: 'user_002' },
          { userId: 'user_003' }
        ],
        category: 'activity',
        createdAt: '2024-04-03T09:00:00Z',
        updatedAt: '2024-04-03T09:00:00Z',
      },
      {
        id: 'exp_004',
        tripId: 'trip_001',
        description: 'مواصلات',
        amount: 300,
        currency: 'EGP',
        date: '2024-04-01',
        paidBy: ['user_001'],
        splitBetween: [
          { userId: 'user_001' },
          { userId: 'user_002' },
          { userId: 'user_003' }
        ],
        category: 'transport',
        createdAt: '2024-04-01T08:00:00Z',
        updatedAt: '2024-04-01T08:00:00Z',
      }
    ],
    createdAt: '2024-03-25T10:00:00Z',
    updatedAt: '2024-04-03T09:00:00Z',
  },
  {
    id: 'trip_002',
    name: 'رحلة الإسكندرية',
    startDate: '2024-05-15',
    endDate: '2024-05-17',
    members: sampleUsers,
    expenses: [
      {
        id: 'exp_005',
        tripId: 'trip_002',
        description: 'فندق الكورنيش',
        amount: 800,
        currency: 'EGP',
        date: '2024-05-15',
        paidBy: ['user_001', 'user_002'],
        splitBetween: [
          { userId: 'user_001' },
          { userId: 'user_002' },
          { userId: 'user_003' },
          { userId: 'user_004' }
        ],
        category: 'accommodation',
        createdAt: '2024-05-15T14:00:00Z',
        updatedAt: '2024-05-15T14:00:00Z',
      },
      {
        id: 'exp_006',
        tripId: 'trip_002',
        description: 'مطعم محمد أحمد',
        amount: 320,
        currency: 'EGP',
        date: '2024-05-16',
        paidBy: ['user_003'],
        splitBetween: [
          { userId: 'user_001' },
          { userId: 'user_002' },
          { userId: 'user_003' },
          { userId: 'user_004' }
        ],
        category: 'food',
        createdAt: '2024-05-16T20:00:00Z',
        updatedAt: '2024-05-16T20:00:00Z',
      }
    ],
    createdAt: '2024-05-10T10:00:00Z',
    updatedAt: '2024-05-16T20:00:00Z',
  }
];

export const loadSampleData = async (): Promise<void> => {
  const { saveTrips } = await import('./storage');
  await saveTrips(sampleTrips);
};
